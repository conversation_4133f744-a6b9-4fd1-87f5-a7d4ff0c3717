import { useState } from 'react'
import { FastAuthService } from '@/lib/auth'

interface UseEntrepriseLogoProps {
  entrepriseId: number
  onSuccess?: () => void
}

export function useEntrepriseLogo({ entrepriseId, onSuccess }: UseEntrepriseLogoProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const uploadLogo = async (file: File) => {
    setLoading(true)
    setError(null)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = FastAuthService.getToken()

      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch(
        `${API_BASE_URL}/api/v1/entreprises-tiers/${entrepriseId}/upload-logo`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          },
          body: formData
        }
      )

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `Erreur HTTP: ${response.status}`)
      }

      const result = await response.json()
      
      if (onSuccess) {
        onSuccess()
      }

      return result

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de l\'upload'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const deleteLogo = async () => {
    setLoading(true)
    setError(null)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = FastAuthService.getToken()

      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      const response = await fetch(
        `${API_BASE_URL}/api/v1/entreprises-tiers/${entrepriseId}/logo`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      )

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `Erreur HTTP: ${response.status}`)
      }

      const result = await response.json()
      
      if (onSuccess) {
        onSuccess()
      }

      return result

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la suppression'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    uploadLogo,
    deleteLogo,
    loading,
    error
  }
}
