import { useState, useEffect, useCallback } from 'react'
import { FastAuthService } from '@/lib/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export interface EntrepriseTiers {
  id: number
  nom_entreprise: string
  activite?: string
  adresse?: string
  code_postal?: string
  ville?: string
  pays?: string
  telephone?: string
  fax?: string
  email?: string
  siret?: string
  tva_intracommunautaire?: string
  representant_legal_id?: number
  logo_url?: string
  logo_filename?: string
  workspace_id: number
  is_active: boolean
  created_at: string
  updated_at: string
  created_by?: number
  representant_legal_nom?: string
  representant_legal_email?: string
}

export function useEntreprisesTiers() {
  const [entreprises, setEntreprises] = useState<EntrepriseTiers[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchEntreprises = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const token = FastAuthService.getToken()
      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      console.log('🔄 Récupération des entreprises tierces...')
      
      const response = await fetch(`${API_BASE_URL}/api/v1/entreprises-tiers/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log('📊 Réponse entreprises tierces:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erreur API entreprises tierces:', errorText)
        throw new Error(`Erreur lors de la récupération des entreprises tierces: ${response.status}`)
      }

      const data = await response.json()
      console.log('✅ Entreprises tierces récupérées:', data.length)
      setEntreprises(data)
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la récupération des entreprises tierces'
      console.error('❌ Erreur:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchEntreprises()
  }, [fetchEntreprises])

  return {
    entreprises,
    loading,
    error,
    refresh: fetchEntreprises
  }
}
