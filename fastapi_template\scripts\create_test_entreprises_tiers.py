#!/usr/bin/env python3
"""
Script pour créer des entreprises tierces de test
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au PYTHONPATH
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import AsyncSessionLocal
from app.models.entreprise_tiers import EntrepriseTiers
from app.models.workspace import Workspace

async def create_test_entreprises_tiers():
    """Créer des entreprises tierces de test"""
    
    print("🔄 Création d'entreprises tierces de test...")
    
    async with AsyncSessionLocal() as session:
        try:
            # Récupérer le premier workspace disponible
            workspace_result = await session.execute(
                select(Workspace).where(Workspace.is_active == True).limit(1)
            )
            workspace = workspace_result.scalar_one_or_none()
            
            if not workspace:
                print("❌ Aucun workspace trouvé")
                return
            
            print(f"🔍 Utilisation du workspace: {workspace.name} (ID: {workspace.id})")
            
            # Vérifier si des entreprises tierces existent déjà
            existing_result = await session.execute(
                select(EntrepriseTiers).where(EntrepriseTiers.workspace_id == workspace.id)
            )
            existing_count = len(existing_result.scalars().all())
            
            if existing_count > 0:
                print(f"✅ {existing_count} entreprises tierces existent déjà")
                return
            
            # Créer des entreprises tierces de test
            entreprises_data = [
                {
                    "nom_entreprise": "SARL Maçonnerie Dupont",
                    "activite": "Maçonnerie générale",
                    "adresse": "123 Rue de la Construction",
                    "code_postal": "75001",
                    "ville": "Paris",
                    "pays": "France",
                    "telephone": "01.23.45.67.89",
                    "email": "<EMAIL>",
                    "siret": "12345678901234",
                    "workspace_id": workspace.id,
                    "is_active": True
                },
                {
                    "nom_entreprise": "Électricité Martin & Fils",
                    "activite": "Installation électrique",
                    "adresse": "456 Avenue des Artisans",
                    "code_postal": "69001",
                    "ville": "Lyon",
                    "pays": "France",
                    "telephone": "04.56.78.90.12",
                    "email": "<EMAIL>",
                    "siret": "23456789012345",
                    "workspace_id": workspace.id,
                    "is_active": True
                },
                {
                    "nom_entreprise": "Plomberie Moderne SARL",
                    "activite": "Plomberie et chauffage",
                    "adresse": "789 Boulevard des Métiers",
                    "code_postal": "13001",
                    "ville": "Marseille",
                    "pays": "France",
                    "telephone": "04.91.23.45.67",
                    "email": "<EMAIL>",
                    "siret": "34567890123456",
                    "workspace_id": workspace.id,
                    "is_active": True
                },
                {
                    "nom_entreprise": "Menuiserie Bois & Design",
                    "activite": "Menuiserie sur mesure",
                    "adresse": "321 Rue du Bois",
                    "code_postal": "33000",
                    "ville": "Bordeaux",
                    "pays": "France",
                    "telephone": "05.67.89.01.23",
                    "email": "<EMAIL>",
                    "siret": "45678901234567",
                    "workspace_id": workspace.id,
                    "is_active": True
                }
            ]
            
            created_count = 0
            for entreprise_data in entreprises_data:
                entreprise = EntrepriseTiers(**entreprise_data)
                session.add(entreprise)
                created_count += 1
            
            await session.commit()
            print(f"✅ {created_count} entreprises tierces créées avec succès!")
            
            # Afficher les entreprises créées
            result = await session.execute(
                select(EntrepriseTiers).where(EntrepriseTiers.workspace_id == workspace.id)
            )
            entreprises = result.scalars().all()
            
            print("\n📋 Entreprises tierces créées:")
            for entreprise in entreprises:
                print(f"  - {entreprise.nom_entreprise} (ID: {entreprise.id}) - {entreprise.activite}")
            
        except Exception as e:
            print(f"❌ Erreur lors de la création: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()

if __name__ == "__main__":
    asyncio.run(create_test_entreprises_tiers())
