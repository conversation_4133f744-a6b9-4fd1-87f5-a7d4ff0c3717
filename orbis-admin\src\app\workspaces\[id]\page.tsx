'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { FastWorkspaceService } from '@/lib/fast-auth'

export default function WorkspaceDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const workspaceId = parseInt(params.id as string)

  const [workspace, setWorkspace] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadWorkspace()
  }, [workspaceId])

  const loadWorkspace = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await FastWorkspaceService.getWorkspace(workspaceId)
      setWorkspace(data)
    } catch (err) {
      console.error('Erreur chargement workspace:', err)
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!workspace) return

    if (confirm(`Êtes-vous sûr de vouloir supprimer le workspace "${workspace.name}" ?`)) {
      try {
        await FastWorkspaceService.deleteWorkspace(workspaceId)
        router.push('/workspaces')
      } catch (err) {
        console.error('Erreur suppression workspace:', err)
        setError(err instanceof Error ? err.message : 'Erreur lors de la suppression')
      }
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <button onClick={() => router.back()} className="text-blue-600 hover:text-blue-800">
            ← Retour
          </button>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-600">Chargement...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <button onClick={() => router.back()} className="text-blue-600 hover:text-blue-800">
            ← Retour
          </button>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={loadWorkspace}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Réessayer
          </button>
        </div>
      </div>
    )
  }

  if (!workspace) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <button onClick={() => router.back()} className="text-blue-600 hover:text-blue-800">
            ← Retour
          </button>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-600">Workspace non trouvé</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <button onClick={() => router.back()} className="text-blue-600 hover:text-blue-800">
            ← Retour
          </button>
          <div>
            <h1 className="text-2xl font-bold">{workspace.name}</h1>
            <p className="text-gray-600">{workspace.description}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <span className={`px-2 py-1 rounded text-sm ${workspace.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
            {workspace.is_active ? "Actif" : "Inactif"}
          </span>
          <button
            onClick={() => router.push(`/workspaces/${workspaceId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Modifier
          </button>
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Supprimer
          </button>
        </div>
      </div>

      {/* Informations */}
      <div className="bg-white border rounded-lg p-6">
        <h2 className="text-lg font-semibold mb-4">Informations générales</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-700">Nom</label>
            <p className="text-sm text-gray-900">{workspace.name}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Code</label>
            <p className="text-sm text-gray-900">{workspace.code || 'N/A'}</p>
          </div>
          <div className="md:col-span-2">
            <label className="text-sm font-medium text-gray-700">Description</label>
            <p className="text-sm text-gray-900">{workspace.description || 'Aucune description'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Statut</label>
            <p className="text-sm text-gray-900">
              {workspace.is_active ? 'Actif' : 'Inactif'}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Créé le</label>
            <p className="text-sm text-gray-900">
              {workspace.created_at ? new Date(workspace.created_at).toLocaleDateString('fr-FR') : 'N/A'}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
