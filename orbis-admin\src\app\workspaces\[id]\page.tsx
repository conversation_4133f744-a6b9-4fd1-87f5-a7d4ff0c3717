'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ArrowLeft, Users, Settings, Activity, Building2, Plus, Edit, Trash2, Shield } from 'lucide-react'
import { FastWorkspaceService, FastAdminService, RBACService } from '@/lib/fast-auth'
import { useToast } from '@/components/ui/Toast'

export default function WorkspaceDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const workspaceId = parseInt(params.id as string)
  const { success, error: showError } = useToast()

  const [workspace, setWorkspace] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [admins, setAdmins] = useState<any[]>([])
  const [loadingAdmins, setLoadingAdmins] = useState(false)
  const [showAddAdmin, setShowAddAdmin] = useState(false)
  const [editingAdmin, setEditingAdmin] = useState<any>(null)

  useEffect(() => {
    loadWorkspace()
    loadAdmins()
  }, [workspaceId])

  const loadWorkspace = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await FastWorkspaceService.getWorkspace(workspaceId)
      setWorkspace(data)
    } catch (err) {
      console.error('Erreur chargement workspace:', err)
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
    } finally {
      setLoading(false)
    }
  }

  const loadAdmins = async () => {
    try {
      setLoadingAdmins(true)
      const data = await FastAdminService.getCompanyUsers(workspaceId)
      setAdmins(data)
    } catch (err) {
      console.error('Erreur chargement administrateurs:', err)
    } finally {
      setLoadingAdmins(false)
    }
  }

  const handleCreateAdmin = async (adminData: any) => {
    try {
      await FastAdminService.createUser({
        ...adminData,
        workspace_id: workspaceId
      })
      success('Succès', 'Administrateur créé avec succès')
      setShowAddAdmin(false)
      await loadAdmins()
    } catch (err) {
      console.error('Erreur création admin:', err)
      showError('Erreur', err instanceof Error ? err.message : 'Erreur lors de la création')
    }
  }

  const handleUpdateAdmin = async (adminData: any) => {
    if (!editingAdmin) return
    try {
      await FastAdminService.updateUser(editingAdmin.id, adminData)
      success('Succès', 'Administrateur modifié avec succès')
      setEditingAdmin(null)
      await loadAdmins()
    } catch (err) {
      console.error('Erreur modification admin:', err)
      showError('Erreur', err instanceof Error ? err.message : 'Erreur lors de la modification')
    }
  }

  const handleDeleteAdmin = async (adminId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet administrateur ?')) return
    try {
      await FastAdminService.deleteUser(adminId)
      success('Succès', 'Administrateur supprimé avec succès')
      await loadAdmins()
    } catch (err) {
      console.error('Erreur suppression admin:', err)
      showError('Erreur', err instanceof Error ? err.message : 'Erreur lors de la suppression')
    }
  }

  const handleDelete = async () => {
    if (!workspace) return

    if (confirm(`Êtes-vous sûr de vouloir supprimer le workspace "${workspace.name}" ?`)) {
      try {
        await FastWorkspaceService.deleteWorkspace(workspaceId)
        router.push('/workspaces')
      } catch (err) {
        console.error('Erreur suppression workspace:', err)
        setError(err instanceof Error ? err.message : 'Erreur lors de la suppression')
      }
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
        </div>
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement du workspace...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={loadWorkspace}>Réessayer</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!workspace) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Workspace non trouvé</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{workspace.name}</h1>
            <p className="text-gray-600 mt-1">{workspace.description}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant={workspace.is_active ? "success" : "secondary"}>
            {workspace.is_active ? "Actif" : "Inactif"}
          </Badge>
          <Button variant="outline" onClick={() => router.push(`/workspaces/${workspaceId}/edit`)}>
            <Settings className="h-4 w-4 mr-2" />
            Modifier
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            Supprimer
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{workspace.user_count || 0}</div>
            <p className="text-xs text-gray-600 mt-1">
              Utilisateurs actifs
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Projets</CardTitle>
            <Activity className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{workspace.project_count || 0}</div>
            <p className="text-xs text-gray-600 mt-1">
              Projets en cours
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Créé le</CardTitle>
            <Building2 className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {workspace.created_at ? new Date(workspace.created_at).toLocaleDateString('fr-FR') : 'N/A'}
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Dernière mise à jour: {workspace.updated_at ? new Date(workspace.updated_at).toLocaleDateString('fr-FR') : 'N/A'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="admins">Administrateurs</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
          <TabsTrigger value="settings">Paramètres</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Informations générales</CardTitle>
              <CardDescription>
                Détails et configuration du workspace
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Nom du workspace</label>
                  <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{workspace.name}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Code unique</label>
                  <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{workspace.code || 'Non défini'}</p>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Description</label>
                <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[60px]">
                  {workspace.description || 'Aucune description disponible'}
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Statut</label>
                  <div className="flex items-center gap-2">
                    <Badge variant={workspace.is_active ? "success" : "secondary"}>
                      {workspace.is_active ? 'Actif' : 'Inactif'}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Date de création</label>
                  <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                    {workspace.created_at ? new Date(workspace.created_at).toLocaleDateString('fr-FR', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) : 'Non disponible'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="admins" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Administrateurs du workspace</CardTitle>
                <CardDescription>
                  Gérez les utilisateurs ayant des droits d'administration sur ce workspace
                </CardDescription>
              </div>
              <Button onClick={() => setShowAddAdmin(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Ajouter un admin
              </Button>
            </CardHeader>
            <CardContent>
              {loadingAdmins ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-4 text-gray-600">Chargement des administrateurs...</p>
                </div>
              ) : admins.length > 0 ? (
                <div className="space-y-3">
                  {admins.map((admin: any) => (
                    <div key={admin.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <Users className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{admin.first_name} {admin.last_name}</p>
                          <p className="text-sm text-gray-600">{admin.email}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">{admin.role}</Badge>
                            {admin.company_role && (
                              <Badge variant="secondary" className="text-xs">{admin.company_role}</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingAdmin(admin)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteAdmin(admin.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-4">Aucun administrateur trouvé</p>
                  <Button onClick={() => setShowAddAdmin(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Ajouter le premier admin
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <PermissionsTab workspaceId={workspaceId} />
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres du workspace</CardTitle>
              <CardDescription>
                Configuration et options avancées
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Fonctionnalités de configuration à venir...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Composant pour l'onglet permissions
function PermissionsTab({ workspaceId }: { workspaceId: number }) {
  const router = useRouter()
  const { success, error: showError } = useToast()
  const [permissions, setPermissions] = useState<any[]>([])
  const [roles, setRoles] = useState<string[]>([])
  const [selectedRole, setSelectedRole] = useState<string | null>(null)
  const [rolePermissions, setRolePermissions] = useState<any>({})
  const [loading, setLoading] = useState(true)

  // Labels pour les ressources
  const RESOURCE_LABELS = {
    'projects': 'Projets (Dossiers)',
    'users': 'Utilisateurs',
    'documents': 'Documents',
    'companies': 'Entreprises tierces',
    'budgets': 'Budgets',
    'quotes': 'Devis',
    'purchase_orders': 'Bons de commande',
    'invoices': 'Factures',
    'reports': 'Rapports',
    'settings': 'Paramètres',
    'admin': 'Administration'
  }

  // Labels pour les actions
  const ACTION_LABELS = {
    'create': 'Créer',
    'read': 'Voir',
    'update': 'Modifier',
    'delete': 'Supprimer',
    'approve': 'Approuver',
    'send': 'Envoyer',
    'validate': 'Valider',
    'download': 'Télécharger',
    'upload': 'Uploader',
    'manage': 'Gérer',
    'assign': 'Assigner'
  }

  useEffect(() => {
    loadPermissions()
  }, [workspaceId])

  const loadPermissions = async () => {
    try {
      setLoading(true)

      // Charger les rôles disponibles
      const rolesData = await RBACService.getRoles()
      setRoles(rolesData.map((role: any) => role.name))

      // Sélectionner le premier rôle par défaut
      if (rolesData.length > 0) {
        setSelectedRole(rolesData[0].name)
      }

      // Charger toutes les permissions
      const permissionsData = await RBACService.getPermissions()
      setPermissions(permissionsData)

      // Charger la matrice des permissions pour ce workspace
      const matrixData = await RBACService.getCompanyPermissionMatrix(workspaceId)
      setRolePermissions(matrixData.matrix || {})

    } catch (error) {
      console.error('❌ Erreur chargement permissions:', error)
      showError('Erreur', 'Impossible de charger les permissions')
    } finally {
      setLoading(false)
    }
  }

  // Grouper les permissions par ressource
  const permissionsByResource = permissions.reduce((acc: any, permission: any) => {
    if (!acc[permission.resource]) {
      acc[permission.resource] = []
    }
    acc[permission.resource].push(permission)
    return acc
  }, {})

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des permissions...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Liste des rôles */}
      <div className="lg:col-span-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              Rôles ({roles.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-2">
            {roles.map((role) => (
              <button
                key={role}
                onClick={() => setSelectedRole(role)}
                className={`w-full text-left p-3 rounded-lg transition-colors mb-1 ${
                  selectedRole === role
                    ? 'bg-blue-50 border border-blue-200 text-blue-900'
                    : 'hover:bg-gray-50 text-gray-700'
                }`}
              >
                <div className="font-medium">{role}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {(rolePermissions[role] || []).length} permissions
                </div>
              </button>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Permissions */}
      <div className="lg:col-span-3">
        {selectedRole ? (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Permissions pour le rôle : {selectedRole}</CardTitle>
                <CardDescription>
                  {(rolePermissions[selectedRole] || []).length} permissions accordées sur {permissions.length} disponibles
                </CardDescription>
              </div>
              <Button onClick={() => router.push(`/workspaces/${workspaceId}/roles`)}>
                <Settings className="h-4 w-4 mr-2" />
                Modifier
              </Button>
            </CardHeader>
            <CardContent className="space-y-6">
              {Object.entries(permissionsByResource).map(([resource, resourcePermissions]: [string, any]) => (
                <div key={resource} className="border border-gray-200 rounded-lg">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="font-medium text-gray-900">
                      {RESOURCE_LABELS[resource as keyof typeof RESOURCE_LABELS] || resource}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">
                      {resourcePermissions.length} actions disponibles
                    </p>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {resourcePermissions.map((permission: any) => {
                        const isGranted = (rolePermissions[selectedRole] || []).includes(permission.name)
                        return (
                          <div
                            key={permission.name}
                            className={`flex items-start gap-3 p-3 rounded-lg border ${
                              isGranted
                                ? 'bg-green-50 border-green-200 text-green-900'
                                : 'bg-gray-50 border-gray-200'
                            }`}
                          >
                            <div className="mt-1">
                              {isGranted ? (
                                <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                  <div className="w-2 h-2 bg-white rounded-full"></div>
                                </div>
                              ) : (
                                <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-sm">
                                {ACTION_LABELS[permission.action as keyof typeof ACTION_LABELS] || permission.action}
                              </div>
                              <p className="text-xs text-gray-500 mt-1 truncate">
                                {permission.description}
                              </p>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Sélectionnez un rôle</h3>
                <p className="text-gray-600">
                  Choisissez un rôle dans la liste de gauche pour voir ses permissions.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
