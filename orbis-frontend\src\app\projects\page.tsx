'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { FastAuthService } from '@/lib/auth'
import { projectsAPI, Project } from '@/lib/api/projects'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import {
  Plus,
  Search,
  Filter,
  Building2,
  Calendar,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  MoreVertical,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

// Types pour les dossiers (utilise le type Project de l'API)
type Dossier = Project

function DossiersPageContent() {
  const { user, signOut } = useAuth()
  const searchParams = useSearchParams()
  const [dossiers, setDossiers] = useState<Dossier[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterNature, setFilterNature] = useState('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [stats, setStats] = useState<any>(null)

  // Gérer les filtres depuis l'URL
  useEffect(() => {
    const urlFilter = searchParams.get('filter')
    if (urlFilter === 'devis') {
      setFilterNature('Devis')
    } else if (urlFilter === 'ao') {
      setFilterNature('AO')
    } else if (urlFilter === 'affaires') {
      setFilterNature('Affaire')
    }
  }, [searchParams])

  useEffect(() => {
    const fetchDossiers = async () => {
      try {
        setLoading(true)

        // Récupérer les projets et les statistiques en parallèle
        const [projectsData, statsData] = await Promise.all([
          projectsAPI.getProjects({
            limit: 1000 // Récupérer tous les projets pour l'instant
          }),
          projectsAPI.getProjectsStats()
        ])

        setDossiers(projectsData || [])
        setStats(statsData)
        setError(null)
      } catch (err: any) {
        console.error('Error fetching dossiers:', err)
        setError(err.message || 'Erreur lors du chargement des dossiers')
        setDossiers([])
        setStats(null)
      } finally {
        setLoading(false)
      }
    }

    fetchDossiers()
  }, [])

  // Fonction de déconnexion
  const handleLogout = () => {
    signOut()
  }

  // Fonction pour formater les montants
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Fonctions de conversion entre backend et frontend
  const convertStatusToDisplay = (status: string) => {
    const statusMap: Record<string, string> = {
      'EN_COURS': 'En cours',
      'EN_ATTENTE': 'En attente',
      'TERMINE': 'Terminé',
      'ARCHIVE': 'Archivé'
    }
    return statusMap[status] || status
  }

  const convertNatureToDisplay = (nature: string) => {
    const natureMap: Record<string, string> = {
      'DEVIS': 'Devis',
      'AO': 'AO',
      'AFFAIRE': 'Affaire'
    }
    return natureMap[nature] || nature
  }

  const convertStatusToBackend = (status: string) => {
    const statusMap: Record<string, string> = {
      'En cours': 'EN_COURS',
      'En attente': 'EN_ATTENTE',
      'Terminé': 'TERMINE',
      'Archivé': 'ARCHIVE'
    }
    return statusMap[status] || status
  }

  const convertNatureToBackend = (nature: string) => {
    const natureMap: Record<string, string> = {
      'Devis': 'DEVIS',
      'AO': 'AO',
      'Affaire': 'AFFAIRE'
    }
    return natureMap[nature] || nature
  }

  // Filtrage des dossiers
  const filteredDossiers = dossiers.filter((dossier: Dossier) => {
    const matchesSearch = dossier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dossier.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dossier.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dossier.code.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === 'all' || convertStatusToDisplay(dossier.status) === filterStatus
    const matchesNature = filterNature === 'all' || convertNatureToDisplay(dossier.nature) === filterNature
    return matchesSearch && matchesStatus && matchesNature
  })

  // Fonction pour obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En cours':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'Terminé':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'En attente':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Fonction pour obtenir l'icône du statut
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'En cours':
        return <Clock className="w-4 h-4" />
      case 'Terminé':
        return <CheckCircle className="w-4 h-4" />
      case 'En attente':
        return <AlertCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // Fonction pour obtenir la couleur de la nature
  const getNatureColor = (nature: string) => {
    switch (nature) {
      case 'Devis':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'AO':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'Affaire':
        return 'bg-teal-100 text-teal-800 border-teal-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Fonction pour obtenir l'icône de la nature
  const getNatureIcon = (nature: string) => {
    switch (nature) {
      case 'Devis':
        return '📋'
      case 'AO':
        return '📢'
      case 'Affaire':
        return '🤝'
      default:
        return '📁'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />
          <div className="flex-1 lg:ml-72">
            <ModernHeader
              title="Dossiers"
              subtitle="Chargement des dossiers..."
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={handleLogout}
            />
            <main className="p-6">
              <div className="flex justify-center items-center min-h-96">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                  <div className="text-lg text-gray-600">Chargement des dossiers...</div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </div>
    )
  }

  if (error && dossiers.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />
          <div className="flex-1 lg:ml-72">
            <ModernHeader
              title="Dossiers"
              subtitle="Erreur de chargement"
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={handleLogout}
            />
            <main className="p-6">
              <div className="flex justify-center items-center min-h-96">
                <div className="text-center max-w-md">
                  <div className="bg-red-50 border border-red-200 rounded-2xl p-8">
                    <div className="text-4xl mb-4">⚠️</div>
                    <div className="text-red-800 text-lg font-semibold mb-2">Erreur de chargement</div>
                    <div className="text-red-600 mb-6">{error}</div>
                    <button
                      onClick={() => window.location.reload()}
                      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      Réessayer
                    </button>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        <ModernSidebar user={user ? {
          name: `${user.first_name} ${user.last_name}`,
          email: user.email
        } : undefined} />

        <div className="flex-1 lg:ml-72">
          <ModernHeader
            title="Dossiers"
            subtitle={`Gestion de vos dossiers (Devis, AO, Affaires) • ${filteredDossiers.length} dossier${filteredDossiers.length > 1 ? 's' : ''}`}
            user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined}
            onLogout={handleLogout}
          />

          <main className="p-6">
            <div className="space-y-6">

              {/* Header avec actions */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-primary-100 rounded-xl">
                      <Building2 className="w-6 h-6 text-primary-600" />
                    </div>
                    <div>
                      <h1 className="text-2xl font-bold text-gray-900">Mes Dossiers</h1>
                      <p className="text-gray-600">Gérez vos devis, appels d'offres et affaires</p>
                    </div>
                  </div>
                  <Link href="/projects/create">
                    <button className="flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-xl transition-colors">
                      <Plus className="w-4 h-4 mr-2" />
                      Nouveau Dossier
                    </button>
                  </Link>
                </div>
              </div>

              {/* Statistiques rapides */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Budget Total</p>
                      <p className="text-2xl font-bold text-green-700">
                        {stats ? formatCurrency(stats.total_budget) : formatCurrency(0)}
                      </p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <Building2 className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Devis</p>
                      <p className="text-2xl font-bold text-green-600">
                        {stats ? formatCurrency(stats.budgets_by_nature?.DEVIS || 0) : formatCurrency(0)}
                      </p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="w-6 h-6 text-green-500 text-lg flex items-center justify-center">📋</div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Appels d'Offres</p>
                      <p className="text-2xl font-bold text-green-600">
                        {stats ? formatCurrency(stats.budgets_by_nature?.AO || 0) : formatCurrency(0)}
                      </p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="w-6 h-6 text-green-500 text-lg flex items-center justify-center">📢</div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Affaires</p>
                      <p className="text-2xl font-bold text-green-600">
                        {stats ? formatCurrency(stats.budgets_by_nature?.AFFAIRE || 0) : formatCurrency(0)}
                      </p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="w-6 h-6 text-green-500 text-lg flex items-center justify-center">🤝</div>
                    </div>
                  </div>
                </div>


              </div>

              {/* Boutons de tri par nature */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Filtrer par type</h3>
                    <p className="text-sm text-gray-600">Sélectionnez le type de dossier à afficher</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => setFilterNature('all')}
                      className={`px-4 py-2 rounded-lg font-medium transition-all ${
                        filterNature === 'all'
                          ? 'bg-green-600 text-white shadow-md'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      TOUT ({stats ? stats.total_active : dossiers.length})
                    </button>
                    <button
                      onClick={() => setFilterNature('Devis')}
                      className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center ${
                        filterNature === 'Devis'
                          ? 'bg-green-600 text-white shadow-md'
                          : 'bg-green-50 text-green-700 hover:bg-green-100'
                      }`}
                    >
                      <span className="mr-2">📋</span>
                      DEVIS ({stats ? stats.by_nature?.DEVIS || 0 : dossiers.filter(d => convertNatureToDisplay(d.nature) === 'Devis').length})
                    </button>
                    <button
                      onClick={() => setFilterNature('AO')}
                      className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center ${
                        filterNature === 'AO'
                          ? 'bg-green-600 text-white shadow-md'
                          : 'bg-green-50 text-green-700 hover:bg-green-100'
                      }`}
                    >
                      <span className="mr-2">📢</span>
                      AO ({stats ? stats.by_nature?.AO || 0 : dossiers.filter(d => convertNatureToDisplay(d.nature) === 'AO').length})
                    </button>
                    <button
                      onClick={() => setFilterNature('Affaire')}
                      className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center ${
                        filterNature === 'Affaire'
                          ? 'bg-green-600 text-white shadow-md'
                          : 'bg-green-50 text-green-700 hover:bg-green-100'
                      }`}
                    >
                      <span className="mr-2">🤝</span>
                      AFFAIRES ({stats ? stats.by_nature?.AFFAIRE || 0 : dossiers.filter(d => convertNatureToDisplay(d.nature) === 'Affaire').length})
                    </button>
                  </div>
                </div>
              </div>

              {/* Filtres et recherche */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Rechercher un dossier, client, code ou description..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                  <div className="flex gap-3">
                    <select
                      value={filterNature}
                      onChange={(e) => setFilterNature(e.target.value)}
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="all">Toutes les natures</option>
                      <option value="Devis">📋 Devis</option>
                      <option value="AO">📢 Appels d'Offres</option>
                      <option value="Affaire">🤝 Affaires</option>
                    </select>
                    <select
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value)}
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="all">Tous les statuts</option>
                      <option value="En cours">En cours</option>
                      <option value="En attente">En attente</option>
                      <option value="Terminé">Terminé</option>
                    </select>
                    <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                      <button
                        onClick={() => setViewMode('grid')}
                        className={`px-3 py-2 ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'} transition-colors`}
                      >
                        <div className="w-4 h-4 grid grid-cols-2 gap-0.5">
                          <div className="bg-current rounded-sm"></div>
                          <div className="bg-current rounded-sm"></div>
                          <div className="bg-current rounded-sm"></div>
                          <div className="bg-current rounded-sm"></div>
                        </div>
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`px-3 py-2 ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'} transition-colors`}
                      >
                        <div className="w-4 h-4 flex flex-col gap-1">
                          <div className="bg-current h-0.5 rounded"></div>
                          <div className="bg-current h-0.5 rounded"></div>
                          <div className="bg-current h-0.5 rounded"></div>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Liste des dossiers */}
              {filteredDossiers.length > 0 ? (
                <div className={viewMode === 'grid'
                  ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                  : "space-y-4"
                }>
                  {filteredDossiers.map((dossier: Dossier) => (
                    viewMode === 'grid' ? (
                      // Vue en grille
                      <div key={dossier.id} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
                        <div className="p-6">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h3 className="text-lg font-semibold text-gray-900">{dossier.name}</h3>
                                <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getNatureColor(dossier.nature)}`}>
                                  <span className="mr-1">{getNatureIcon(dossier.nature)}</span>
                                  {dossier.nature}
                                </div>
                              </div>
                              <p className="text-sm text-gray-500 mb-2">{dossier.code}</p>
                              <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusColor(dossier.status)}`}>
                                {getStatusIcon(dossier.status)}
                                <span className="ml-1">{dossier.status}</span>
                              </div>
                            </div>
                            <div className="relative">
                              <button className="p-1 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                                <MoreVertical className="w-4 h-4" />
                              </button>
                            </div>
                          </div>

                          <p className="text-sm text-gray-600 mb-4 line-clamp-2">{dossier.description}</p>

                          <div className="space-y-3">
                            <div className="flex items-center text-sm text-gray-600">
                              <Users className="w-4 h-4 mr-2" />
                              <span>{dossier.client_name}</span>
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <Calendar className="w-4 h-4 mr-2" />
                              <span>{new Date(dossier.start_date).toLocaleDateString('fr-FR')} - {new Date(dossier.end_date).toLocaleDateString('fr-FR')}</span>
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <TrendingUp className="w-4 h-4 mr-2" />
                              <span>{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', minimumFractionDigits: 0 }).format(dossier.budget_total)}</span>
                            </div>
                          </div>

                          <div className="mt-4">
                            <div className="flex items-center justify-between text-sm mb-2">
                              <span className="text-gray-600">Progression</span>
                              <span className="font-medium text-gray-900">{dossier.progress}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-500"
                                style={{ width: `${dossier.progress}%` }}
                              ></div>
                            </div>
                          </div>

                          <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
                            <Link href={`/projects/${dossier.id}`}>
                              <button className="flex items-center text-sm text-primary-600 hover:text-primary-700 font-medium">
                                <Eye className="w-4 h-4 mr-1" />
                                Voir détails
                              </button>
                            </Link>
                            <div className="flex items-center space-x-2">
                              <button className="p-1.5 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                                <Edit className="w-4 h-4" />
                              </button>
                              <button className="p-1.5 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50">
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      // Vue en liste
                      <div key={dossier.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 flex-1">
                            <div className="flex-shrink-0">
                              <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center text-2xl">
                                {getNatureIcon(dossier.nature)}
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-3 mb-1">
                                <h3 className="text-lg font-semibold text-gray-900">{dossier.name}</h3>
                                <span className="text-sm text-gray-500">({dossier.code})</span>
                                <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getNatureColor(dossier.nature)}`}>
                                  <span className="mr-1">{getNatureIcon(dossier.nature)}</span>
                                  {dossier.nature}
                                </div>
                                <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusColor(dossier.status)}`}>
                                  {getStatusIcon(dossier.status)}
                                  <span className="ml-1">{dossier.status}</span>
                                </div>
                              </div>
                              <p className="text-sm text-gray-600 mb-2">{dossier.description}</p>
                              <div className="flex items-center space-x-6 text-sm text-gray-500">
                                <span className="flex items-center">
                                  <Users className="w-4 h-4 mr-1" />
                                  {dossier.client_name}
                                </span>
                                <span className="flex items-center">
                                  <Calendar className="w-4 h-4 mr-1" />
                                  {new Date(dossier.end_date).toLocaleDateString('fr-FR')}
                                </span>
                                <span className="flex items-center">
                                  <TrendingUp className="w-4 h-4 mr-1" />
                                  {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', minimumFractionDigits: 0 }).format(dossier.budget_total)}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <div className="text-sm font-medium text-gray-900">{dossier.progress}%</div>
                              <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                                <div
                                  className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full"
                                  style={{ width: `${dossier.progress}%` }}
                                ></div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Link href={`/projects/${dossier.id}`}>
                                <button className="p-2 text-gray-400 hover:text-primary-600 rounded-lg hover:bg-primary-50">
                                  <Eye className="w-4 h-4" />
                                </button>
                              </Link>
                              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                                <Edit className="w-4 h-4" />
                              </button>
                              <button className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50">
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  ))}
                </div>
              ) : (
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
                  <div className="text-6xl mb-4">📁</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Aucun dossier trouvé</h3>
                  <p className="text-gray-600 mb-6">
                    {searchTerm || filterStatus !== 'all' || filterNature !== 'all'
                      ? 'Essayez de modifier vos critères de recherche'
                      : 'Commencez par créer votre premier dossier'
                    }
                  </p>
                  {!searchTerm && filterStatus === 'all' && filterNature === 'all' && (
                    <Link href="/projects/create">
                      <button className="inline-flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors">
                        <Plus className="w-4 h-4 mr-2" />
                        Créer un dossier
                      </button>
                    </Link>
                  )}
                </div>
              )}

            </div>
          </main>
        </div>
      </div>
    </div>
  )
}

export default function Dossiers() {
  return (
    <ProtectedRoute>
      <DossiersPageContent />
    </ProtectedRoute>
  )
}
