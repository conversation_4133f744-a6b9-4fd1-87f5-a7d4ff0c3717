import { useState, useEffect } from 'react'
import { CompanySimple, EntrepriseTiers } from '@/types/technical-document'
import { FastAuthService } from '@/lib/auth'

interface CompanyWithLogo extends CompanySimple {
  logo_url?: string
  logo_filename?: string
}

export function useCompaniesWithLogos(selectedCompanies: CompanySimple[]) {
  const [companiesWithLogos, setCompaniesWithLogos] = useState<CompanyWithLogo[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCompaniesWithLogos = async () => {
      if (!selectedCompanies || selectedCompanies.length === 0) {
        setCompaniesWithLogos([])
        return
      }

      setLoading(true)
      setError(null)

      try {
        const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
        const token = FastAuthService.getToken()

        if (!token) {
          throw new Error('Token d\'authentification manquant')
        }

        // Récupérer toutes les entreprises tierces
        const response = await fetch(`${API_BASE_URL}/api/v1/entreprises-tiers`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error('Erreur lors du chargement des entreprises')
        }

        const entreprisesTiers: EntrepriseTiers[] = await response.json()

        // Mapper les entreprises sélectionnées avec leurs logos
        const companiesWithLogos: CompanyWithLogo[] = selectedCompanies.map(company => {
          // Trouver l'entreprise tierce correspondante
          const entrepriseTiers = entreprisesTiers.find(et => 
            et.nom_entreprise === company.name || 
            et.siret === company.code ||
            et.id.toString() === company.code.replace('ET-', '')
          )

          return {
            ...company,
            logo_url: entrepriseTiers?.logo_url,
            logo_filename: entrepriseTiers?.logo_filename
          }
        })

        setCompaniesWithLogos(companiesWithLogos)

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement des logos'
        setError(errorMessage)
        console.error('Erreur lors du chargement des logos:', err)
        
        // En cas d'erreur, retourner les entreprises sans logos
        setCompaniesWithLogos(selectedCompanies.map(company => ({ ...company })))
      } finally {
        setLoading(false)
      }
    }

    fetchCompaniesWithLogos()
  }, [selectedCompanies])

  return {
    companiesWithLogos,
    loading,
    error
  }
}
