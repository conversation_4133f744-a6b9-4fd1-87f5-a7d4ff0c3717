'use client'

import React, { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { DocumentType, TechnicalDocumentResponse } from '@/types/technical-document'
import DocumentCreationButtons from '@/components/technical-documents/DocumentCreationButtons'
import TechnicalDocumentEditor from '@/components/editor/TechnicalDocumentEditor'
import { CCTPHeader } from '@/components/technical-documents/CompanyLogos'

import CompanySelector from '@/components/technical-documents/CompanySelector'

import { useTechnicalDocument, useCreateTechnicalDocument } from '@/hooks/useTechnicalDocument'
import { useChatGPT } from '@/hooks/useChatGPT'
import { useEntreprisesTiers, EntrepriseTiers } from '@/hooks/useEntreprisesTiers'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'

function DocumentsTechniquesPageContent() {
  const { user, signOut } = useAuth()
  const searchParams = useSearchParams()
  const router = useRouter()
  const [selectedDocument, setSelectedDocument] = useState<TechnicalDocumentResponse | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [newDocumentType, setNewDocumentType] = useState<DocumentType | null>(null)
  const [newDocumentName, setNewDocumentName] = useState('')
  const [selectedEntreprise, setSelectedEntreprise] = useState<EntrepriseTiers | null>(null)
  const [companiesLoading, setCompaniesLoading] = useState(false)


  // Récupérer les paramètres depuis l'URL
  const selectedProjectId = parseInt(searchParams.get('project_id') || '7')
  const documentIdFromUrl = searchParams.get('document_id') ? parseInt(searchParams.get('document_id')!) : null

  // Hooks pour la gestion des documents
  const { document, loading, error, save, refresh } = useTechnicalDocument(selectedDocument?.id)
  const { createDocument, loading: creating } = useCreateTechnicalDocument()
  const { enhanceText, loading: enhancing } = useChatGPT()
  const { entreprises, loading: entreprisesLoading, error: entreprisesError } = useEntreprisesTiers()

  // Sauvegarde automatique désactivée pour éviter les conflits avec TinyMCE
  // const { addPendingChange, isSaving, lastSaved, hasPendingChanges } = useAutoSave(
  //   selectedDocument,
  //   save,
  //   3000 // 3 secondes de délai
  // )

  // Menu contextuel maintenant géré directement dans TinyMCE

  // Charger le document depuis l'URL au démarrage
  useEffect(() => {
    if (documentIdFromUrl && !selectedDocument) {
      console.log('🔄 Chargement du document depuis l\'URL:', documentIdFromUrl)
      // Créer un objet document temporaire avec juste l'ID pour déclencher le chargement
      setSelectedDocument({ id: documentIdFromUrl } as TechnicalDocumentResponse)
    }
  }, [documentIdFromUrl, selectedDocument])

  // Mettre à jour le document sélectionné quand il est chargé
  useEffect(() => {
    if (document && document.id === selectedDocument?.id) {
      console.log('🔄 Mise à jour du document depuis le hook:', document.id)
      setSelectedDocument(document)
    }
  }, [document, selectedDocument?.id])

  // Le menu contextuel est maintenant géré directement dans TinyMCE

  // Gérer la sélection d'un document
  const handleDocumentSelect = (doc: TechnicalDocumentResponse) => {
    setSelectedDocument(doc)
    setIsCreating(false)
  }

  // Gérer la création d'un nouveau document
  const handleDocumentCreate = (type: DocumentType) => {
    setNewDocumentType(type)
    setIsCreating(true)
    setSelectedDocument(null)
  }

  // Créer un nouveau document
  const handleCreateNewDocument = async (name: string, content: string = '') => {
    if (!newDocumentType) return

    try {
      console.log('🔄 Création du document:', {
        name,
        type: newDocumentType,
        project_id: selectedProjectId,
        entreprise: selectedEntreprise?.nom_entreprise
      })

      const newDoc = await createDocument({
        name,
        type_document: newDocumentType,
        project_id: selectedProjectId,
        content,
        company_ids: selectedEntreprise ? [selectedEntreprise.id] : []
      })

      console.log('✅ Document créé:', newDoc)
      setSelectedDocument(newDoc)
      setIsCreating(false)
      setNewDocumentType(null)
      setNewDocumentName('')

      // Mettre à jour l'URL pour inclure le document_id
      const newUrl = `/documents-techniques?project_id=${selectedProjectId}&document_id=${newDoc.id}`
      router.push(newUrl)

      console.log('🔄 État mis à jour - document sélectionné:', newDoc.id)
      console.log('🔄 URL mise à jour:', newUrl)
    } catch (err) {
      console.error('❌ Erreur lors de la création:', err)
    }
  }

  // Gérer les modifications du contenu (sauvegarde manuelle)
  const handleContentChange = async (content: string) => {
    if (selectedDocument) {
      try {
        await save({ content })
        console.log('Contenu sauvegardé')
      } catch (err) {
        console.error('Erreur lors de la sauvegarde:', err)
      }
    }
  }

  // Gérer les modifications des entreprises
  const handleCompaniesChange = async (companyIds: number[]) => {
    if (selectedDocument) {
      try {
        setCompaniesLoading(true)
        await save({ company_ids: companyIds })
        await refresh()
      } catch (err) {
        console.error('Erreur lors de la mise à jour des entreprises:', err)
      } finally {
        setCompaniesLoading(false)
      }
    }
  }

  // L'amélioration de texte est maintenant gérée directement dans TinyMCE

  // Gérer la sélection de texte pour le menu contextuel
  const handleTextSelection = (selectedText: string) => {
    // Le menu contextuel sera affiché via un clic droit
    // Cette fonction prépare juste le texte sélectionné
  }



  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Documents Techniques</h1>
        <p className="mt-2 text-gray-600">
          Gestion des documents CCTP et DPGF avec amélioration IA
        </p>
      </div>

      <div className="space-y-6">
        {/* Boutons de création */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Créer un document</h2>
          <div className="flex space-x-4">
            <DocumentCreationButtons onDocumentCreate={handleDocumentCreate} />
          </div>
        </div>

        {/* Contenu principal - Éditeur */}
        <div>
          {isCreating ? (
            // Mode création
            <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">
                  Nouveau document {newDocumentType}
                </h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nom du document
                    </label>
                    <input
                      type="text"
                      value={newDocumentName}
                      onChange={(e) => setNewDocumentName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder={`Nom du ${newDocumentType}...`}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          if (newDocumentName.trim() && selectedEntreprise) {
                            handleCreateNewDocument(newDocumentName.trim())
                          }
                        }
                      }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Entreprise tierce associée
                    </label>
                    {entreprisesLoading ? (
                      <div className="text-sm text-gray-500">Chargement des entreprises...</div>
                    ) : entreprisesError ? (
                      <div className="text-sm text-red-500">Erreur: {entreprisesError}</div>
                    ) : (
                      <select
                        value={selectedEntreprise?.id || ''}
                        onChange={(e) => {
                          const entrepriseId = parseInt(e.target.value)
                          const entreprise = entreprises.find(ent => ent.id === entrepriseId)
                          setSelectedEntreprise(entreprise || null)
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        <option value="">Sélectionner une entreprise...</option>
                        {entreprises.map((entreprise) => (
                          <option key={entreprise.id} value={entreprise.id}>
                            {entreprise.nom_entreprise} {entreprise.activite ? `(${entreprise.activite})` : ''}
                          </option>
                        ))}
                      </select>
                    )}
                    {!selectedEntreprise && (
                      <p className="mt-1 text-sm text-gray-500">
                        Vous devez sélectionner une entreprise tierce pour créer le document.
                      </p>
                    )}
                  </div>
                  
                  <div className="flex space-x-3">
                    <button
                      onClick={() => {
                        if (newDocumentName.trim() && selectedEntreprise) {
                          handleCreateNewDocument(newDocumentName.trim())
                        }
                      }}
                      disabled={creating || !newDocumentName.trim() || !selectedEntreprise}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                      {creating ? 'Création...' : 'Créer'}
                    </button>
                    
                    <button
                      onClick={() => {
                        setIsCreating(false)
                        setNewDocumentType(null)
                        setNewDocumentName('')
                        setSelectedEntreprise(null)
                      }}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                    >
                      Annuler
                    </button>
                  </div>
                </div>
              </div>
            ) : selectedDocument ? (
              // Mode édition
              <div className="space-y-6">
                {/* Informations du document */}
                <div className="bg-white rounded-lg shadow p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-semibold">{selectedDocument.name}</h2>
                      <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          selectedDocument.type_document === DocumentType.CCTP
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {selectedDocument.type_document}
                        </span>
                        {selectedDocument.project && (
                          <span>Projet: {selectedDocument.project.name}</span>
                        )}
                      </div>
                    </div>
                    
                    {/* Indicateurs de sauvegarde désactivés */}
                    {/* <div className="flex items-center space-x-2 text-sm">
                      Sauvegarde automatique désactivée
                    </div> */}
                  </div>

                  {/* Sélecteur d'entreprises */}
                  <CompanySelector
                    selectedCompanies={selectedDocument.companies || []}
                    onSelectionChange={(companies) => {
                      const companyIds = companies.map(c => c.id)
                      handleCompaniesChange(companyIds)
                    }}
                    loading={companiesLoading}
                    error={error}
                  />
                </div>

                {/* En-tête avec logos des entreprises (pour CCTP) */}
                {selectedDocument.type_document === DocumentType.CCTP && selectedDocument.companies && selectedDocument.companies.length > 0 && (
                  <CCTPHeader
                    companies={selectedDocument.companies}
                    className="mb-6"
                  />
                )}

                {/* Éditeur */}
                <div className="bg-white rounded-lg shadow">
                  <TechnicalDocumentEditor
                    value={selectedDocument.content || ''}
                    onChange={handleContentChange}
                    documentType={selectedDocument.type_document!}
                    onTextSelection={handleTextSelection}
                  />
                </div>
              </div>
            ) : (
              // État vide
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="text-gray-400 mb-4">
                  <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun document sélectionné
                </h3>
                <p className="text-gray-500">
                  Sélectionnez un document existant ou créez-en un nouveau pour commencer.
                </p>
              </div>
          )}
        </div>

        {/* Menu contextuel maintenant intégré dans TinyMCE */}



        {/* Indicateur de chargement global */}
        {enhancing && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>Amélioration du texte en cours...</span>
            </div>
          </div>
        )}
      </div>
    </div>  
  )
}

export default function DocumentsTechniquesPage() {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          <ModernSidebar />
          <div className="flex-1 lg:ml-72">
            <ModernHeader title="Documents Techniques" />
            <main className="p-6">
              <DocumentsTechniquesPageContent />
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
